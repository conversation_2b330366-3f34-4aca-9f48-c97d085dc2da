const XLSX = require('xlsx');

// 创建示例数据
const sampleData = [
    ['短信模板ID', '模板内容', '备注'],
    ['SMS001', '您好，请点击链接查看详情：https://example.com/details/page1?id=123', '包含完整链接的模板'],
    ['SMS002', '访问我们的网站 https://test.com/home/<USER>', '包含路径的链接'],
    ['SMS003', '下载APP：https://app.store.com/download/mobile/android/v1.2.3', '包含多层路径的链接'],
    ['SMS004', '普通短信内容，没有链接', '无链接内容'],
    ['SMS005', '多个链接：https://site1.com/path1/page 和 https://site2.com/path2/file.html', '包含多个链接'],
    ['SMS006', '联系我们：电话123456，网站https://contact.com/support/help', '混合内容']
];

// 创建工作簿
const wb = XLSX.utils.book_new();
const ws = XLSX.utils.aoa_to_sheet(sampleData);

// 添加工作表
XLSX.utils.book_append_sheet(wb, ws, '短信模板');

// 保存文件
XLSX.writeFile(wb, 'sample_sms_templates.xlsx');

console.log('示例文件已创建: sample_sms_templates.xlsx');
console.log('');
console.log('示例数据包含:');
sampleData.slice(1).forEach((row, index) => {
    console.log(`${index + 1}. ${row[0]}: ${row[1]}`);
});
