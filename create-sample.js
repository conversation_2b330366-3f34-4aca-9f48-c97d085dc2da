const XLSX = require('xlsx');

// 创建示例数据
const sampleData = [
    ['短信模板ID', '模板内容', '备注'],
    ['SMS001', '您好，请点击链接查看详情：https://example.com/details/page1?id=123', '包含完整链接的模板'],
    ['SMS002', '访问我们的网站 https://test.com/home/<USER>', '包含路径的链接'],
    ['SMS003', '下载APP：https://app.store.com/download/mobile/android/v1.2.3', '包含多层路径的链接'],
    ['SMS004', '普通短信内容，没有链接', '无链接内容'],
    ['SMS005', '多个链接：https://site1.com/path1/page 和 https://site2.com/path2/file.html', '包含多个链接'],
    ['SMS006', '联系我们：电话123456，网站https://contact.com/support/help', '混合内容'],
    ['SMS007', '调研链接：https://t.laohu.com/survey/Links 请参与', '包含t.laohu.com子域名'],
    ['SMS008', '访问：http://mp.3qmp.com/other 获取更多信息', '包含数字子域名'],
    ['SMS009', '搜索：https://www.baidu.com/s?wd=test&ie=utf-8', '包含查询参数'],
    ['SMS010', 'API接口：https://api.github.com/repos/user/repo/issues', '包含API路径'],
    ['SMS011', 'CDN资源：https://cdn.jsdelivr.net/npm/package@1.0.0/dist/file.js', '包含版本号路径'],
    ['SMS012', '本地服务：https://localhost:3000/api/users/123', '包含端口号'],
    ['SMS013', '内网地址：https://***********:8080/admin/dashboard', '包含IP和端口']
];

// 创建工作簿
const wb = XLSX.utils.book_new();
const ws = XLSX.utils.aoa_to_sheet(sampleData);

// 添加工作表
XLSX.utils.book_append_sheet(wb, ws, '短信模板');

// 保存文件
XLSX.writeFile(wb, 'sample_sms_templates.xlsx');

console.log('示例文件已创建: sample_sms_templates.xlsx');
console.log('');
console.log('示例数据包含:');
sampleData.slice(1).forEach((row, index) => {
    console.log(`${index + 1}. ${row[0]}: ${row[1]}`);
});
