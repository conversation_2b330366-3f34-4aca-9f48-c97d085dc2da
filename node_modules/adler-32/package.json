{"_from": "<PERSON>ler-32@~1.3.0", "_id": "<PERSON><PERSON>-32@1.3.1", "_inBundle": false, "_integrity": "sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==", "_location": "/adler-32", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "<PERSON>ler-32@~1.3.0", "name": "<PERSON>ler-32", "escapedName": "<PERSON>ler-32", "rawSpec": "~1.3.0", "saveSpec": null, "fetchSpec": "~1.3.0"}, "_requiredBy": ["/cfb", "/xlsx"], "_resolved": "https://registry.npmmirror.com/adler-32/-/adler-32-1.3.1.tgz", "_shasum": "1dbf0b36dda0012189a32b3679061932df1821e2", "_spec": "<PERSON>ler-32@~1.3.0", "_where": "/Users/<USER>/Desktop/未命名文件夹 3/node_modules/xlsx", "author": {"name": "sheetjs"}, "bugs": {"url": "https://github.com/SheetJS/js-adler32/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": "adler32.js"}}, "deprecated": false, "description": "Pure-JS ADLER-32", "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "blanket": "~1.2.3", "codepage": "~1.10.0", "dtslint": "^0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "engines": {"node": ">=0.8"}, "files": ["adler32.js", "LICENSE", "README.md", "types/index.d.ts", "types/*.json"], "homepage": "http://sheetjs.com/opensource", "keywords": ["adler32", "checksum"], "license": "Apache-2.0", "main": "./adler32", "name": "<PERSON>ler-32", "repository": {"type": "git", "url": "git://github.com/SheetJS/js-adler32.git"}, "scripts": {"build": "make", "dtslint": "dtslint types", "lint": "make fullint", "test": "make test"}, "types": "types/index.d.ts", "version": "1.3.1"}