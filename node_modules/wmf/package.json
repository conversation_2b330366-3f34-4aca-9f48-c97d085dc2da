{"_from": "wmf@~1.0.1", "_id": "wmf@1.0.2", "_inBundle": false, "_integrity": "sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw==", "_location": "/wmf", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "wmf@~1.0.1", "name": "wmf", "escapedName": "wmf", "rawSpec": "~1.0.1", "saveSpec": null, "fetchSpec": "~1.0.1"}, "_requiredBy": ["/xlsx"], "_resolved": "https://registry.npmmirror.com/wmf/-/wmf-1.0.2.tgz", "_shasum": "7d19d621071a08c2bdc6b7e688a9c435298cc2da", "_spec": "wmf@~1.0.1", "_where": "/Users/<USER>/Desktop/未命名文件夹 3/node_modules/xlsx", "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "author": {"name": "sheetjs"}, "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "bugs": {"url": "https://github.com/SheetJS/js-wmf/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": "wmf.js"}}, "dependencies": {}, "deprecated": false, "description": "Windows MetaFile (WMF) parser", "devDependencies": {"source-map-loader": "^0.2.4", "uglifyjs-webpack-plugin": "^2.2.0"}, "engines": {"node": ">=0.8"}, "files": ["LICENSE", "README.md", "dist/wmf.js", "dist/wmf.node.js", "dist/wmf.js.map", "dist/wmf.node.js.map"], "homepage": "https://sheetjs.com/", "jsdelivr": "./dist/wmf.js", "keywords": ["wmf", "image", "office", "word"], "license": "Apache-2.0", "main": "./dist/wmf.node.js", "name": "wmf", "repository": {"type": "git", "url": "git://github.com/SheetJS/js-wmf.git"}, "scripts": {}, "types": "types", "unpkg": "./dist/wmf.js", "version": "1.0.2"}