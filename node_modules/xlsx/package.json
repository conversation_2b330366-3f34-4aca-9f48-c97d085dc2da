{"_from": "xlsx", "_id": "xlsx@0.18.5", "_inBundle": false, "_integrity": "sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ==", "_location": "/xlsx", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "xlsx", "name": "xlsx", "escapedName": "xlsx", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/xlsx/-/xlsx-0.18.5.tgz", "_shasum": "16711b9113c848076b8a177022799ad356eba7d0", "_spec": "xlsx", "_where": "/Users/<USER>/Desktop/未命名文件夹 3", "alex": {"allow": ["chinese", "special", "simple", "just", "crash", "wtf", "holes"]}, "author": {"name": "sheetjs"}, "bin": {"xlsx": "bin/xlsx.njs"}, "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "bugs": {"url": "https://github.com/SheetJS/sheetjs/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": "xlsx.js"}}, "dependencies": {"adler-32": "~1.3.0", "cfb": "~1.2.1", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "deprecated": false, "description": "SheetJS Spreadsheet data parser and writer", "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "acorn": "7.4.1", "alex": "8.1.1", "blanket": "~1.2.3", "commander": "~2.17.1", "dtslint": "^0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "exit-on-epipe": "~1.0.1", "fflate": "^0.7.1", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "engines": {"node": ">=0.8"}, "homepage": "https://sheetjs.com/", "jsdelivr": "dist/xlsx.full.min.js", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "license": "Apache-2.0", "main": "xlsx.js", "module": "xlsx.mjs", "name": "xlsx", "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"build": "make", "dtslint": "dtslint types", "lint": "make fullint", "pretest": "npm run lint", "pretest-only": "git submodule init && git submodule update", "test": "npm run tests-only", "tests-only": "make travis"}, "sideEffects": false, "types": "types/index.d.ts", "unpkg": "dist/xlsx.full.min.js", "version": "0.18.5"}