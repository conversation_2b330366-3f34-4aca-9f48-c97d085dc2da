{"_from": "frac@~1.1.2", "_id": "frac@1.1.2", "_inBundle": false, "_integrity": "sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA==", "_location": "/frac", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "frac@~1.1.2", "name": "frac", "escapedName": "frac", "rawSpec": "~1.1.2", "saveSpec": null, "fetchSpec": "~1.1.2"}, "_requiredBy": ["/ssf"], "_resolved": "https://registry.npmmirror.com/frac/-/frac-1.1.2.tgz", "_shasum": "3d74f7f6478c88a1b5020306d747dc6313c74d0b", "_spec": "frac@~1.1.2", "_where": "/Users/<USER>/Desktop/未命名文件夹 3/node_modules/ssf", "author": {"name": "SheetJS"}, "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": "frac.js"}}, "dependencies": {}, "deprecated": false, "description": "Rational approximation with bounded denominator", "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "blanket": "~1.2.3", "codepage": "~1.10.0", "dtslint": "^0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0", "voc": "~1.1.0"}, "engines": {"node": ">=0.8"}, "homepage": "http://sheetjs.com/opensource", "keywords": ["math", "fraction", "rational", "approximation"], "license": "Apache-2.0", "main": "./frac", "name": "frac", "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"build": "make", "dtslint": "dtslint types", "lint": "make fullint", "test": "make test"}, "types": "types", "version": "1.1.2"}