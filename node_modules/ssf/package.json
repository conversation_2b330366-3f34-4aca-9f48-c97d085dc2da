{"_from": "ssf@~0.11.2", "_id": "ssf@0.11.2", "_inBundle": false, "_integrity": "sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g==", "_location": "/ssf", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ssf@~0.11.2", "name": "ssf", "escapedName": "ssf", "rawSpec": "~0.11.2", "saveSpec": null, "fetchSpec": "~0.11.2"}, "_requiredBy": ["/xlsx"], "_resolved": "https://registry.npmmirror.com/ssf/-/ssf-0.11.2.tgz", "_shasum": "0b99698b237548d088fc43cdf2b70c1a7512c06c", "_spec": "ssf@~0.11.2", "_where": "/Users/<USER>/Desktop/未命名文件夹 3/node_modules/xlsx", "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "author": {"name": "sheetjs"}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": "ssf.js"}}, "dependencies": {"frac": "~1.1.2"}, "deprecated": false, "description": "Format data using ECMA-376 spreadsheet Format Codes", "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "blanket": "~1.2.3", "dtslint": "^0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "engines": {"node": ">=0.8"}, "homepage": "http://sheetjs.com/", "keywords": ["format", "sprintf", "spreadsheet"], "license": "Apache-2.0", "main": "./ssf", "name": "ssf", "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"build": "make", "dtslint": "dtslint types", "lint": "make fullint", "test": "make test"}, "types": "types", "version": "0.11.2"}