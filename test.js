const { processLinkContent } = require('./index.js');

console.log('测试链接处理功能');
console.log('================');

const testCases = [
    'https://example.com/details/page1?id=123',
    'https://test.com/home/<USER>',
    'https://app.store.com/download/mobile/android/v1.2.3',
    '普通文本内容，没有链接',
    '多个链接：https://site1.com/path1/page 和 https://site2.com/path2/file.html',
    '联系我们：电话123456，网站https://contact.com/support/help',
    'http://insecure.com/path/to/resource',
    'https://domain.com',
    'https://domain.com/',
    'https://domain.com/path',
    // 新增子域名测试用例
    'https://t.laohu.com/survey/Links',
    'http://mp.3qmp.com/other',
    'https://www.baidu.com/s?wd=test&ie=utf-8',
    'https://api.github.com/repos/user/repo/issues',
    'https://cdn.jsdelivr.net/npm/package@1.0.0/dist/file.js',
    '访问链接：https://t.laohu.com/survey/Links 查看详情',
    '多层子域名：https://sub1.sub2.example.com/path/to/resource?param=value',
    'https://localhost:3000/api/users/123',
    'https://***********:8080/admin/dashboard',
    // 新增不带协议的链接测试用例
    'www.example.com/path/to/page',
    'baidu.com/search?q=test',
    't.laohu.com/survey/Links',
    'api.github.com/repos/user/repo',
    '访问网站：www.google.com/search?q=hello 获取信息',
    '请查看 docs.microsoft.com/zh-cn/windows 文档',
    'cdn.jsdelivr.net/npm/vue@3.0.0/dist/vue.js',
    '多个无协议链接：www.site1.com/page1 和 www.site2.com/page2',
    'stackoverflow.com/questions/12345/how-to-code'
];

testCases.forEach((testCase, index) => {
    const result = processLinkContent(testCase);
    console.log(`\n测试 ${index + 1}:`);
    console.log(`输入: ${testCase}`);
    console.log(`输出: ${result}`);
    console.log(`变化: ${testCase !== result ? '是' : '否'}`);
});
