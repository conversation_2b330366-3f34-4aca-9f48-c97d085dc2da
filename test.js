const { processLinkContent } = require('./index.js');

console.log('测试链接处理功能');
console.log('================');

const testCases = [
    'https://example.com/details/page1?id=123',
    'https://test.com/home/<USER>',
    'https://app.store.com/download/mobile/android/v1.2.3',
    '普通文本内容，没有链接',
    '多个链接：https://site1.com/path1/page 和 https://site2.com/path2/file.html',
    '联系我们：电话123456，网站https://contact.com/support/help',
    'http://insecure.com/path/to/resource',
    'https://domain.com',
    'https://domain.com/',
    'https://domain.com/path'
];

testCases.forEach((testCase, index) => {
    const result = processLinkContent(testCase);
    console.log(`\n测试 ${index + 1}:`);
    console.log(`输入: ${testCase}`);
    console.log(`输出: ${result}`);
    console.log(`变化: ${testCase !== result ? '是' : '否'}`);
});
