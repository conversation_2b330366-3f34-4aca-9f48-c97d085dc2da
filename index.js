const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

/**
 * 处理文本内容，去除链接中域名后第一个"/"之后的内容但保留"/"
 * 支持带协议和不带协议的链接
 * @param {string} text - 要处理的文本
 * @returns {string} - 处理后的文本
 */
function processLinkContent(text) {
    if (typeof text !== 'string') {
        return text;
    }

    // 先处理带协议的链接 (http:// 或 https://)
    const urlWithProtocolRegex = /(https?:\/\/(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?::\d+)?(?:\/[^\s]*)?)/g;

    // 处理不带协议的链接，但要避免匹配已经处理过的带协议链接
    // 匹配模式：域名.顶级域名/路径 或 www.域名.顶级域名/路径
    const urlWithoutProtocolRegex = /(?<!https?:\/\/)(?:^|[\s\u4e00-\u9fff，。！？；：""''（）【】《》、])(((?:www\.)?[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}(?::\d+)?\/[^\s\u4e00-\u9fff，。！？；：""''（）【】《》、]*)/g;

    let result = text;

    // 先处理带协议的链接
    result = result.replace(urlWithProtocolRegex, (match) => {
        try {
            // 使用URL构造函数解析链接
            const url = new URL(match);

            // 返回协议 + 域名 + 端口（如果有）+ "/"
            return `${url.protocol}//${url.host}/`;

        } catch (error) {
            // 如果URL解析失败，使用备用方法
            const protocolEnd = match.indexOf('://') + 3;
            const domainEnd = match.indexOf('/', protocolEnd);

            if (domainEnd !== -1) {
                // 保留到域名后的第一个"/"（包含"/"）
                return match.substring(0, domainEnd + 1);
            } else {
                // 如果没有找到"/"，在域名后添加"/"
                return match + '/';
            }
        }
    });

    // 再处理不带协议的链接
    result = result.replace(urlWithoutProtocolRegex, (fullMatch, linkPart) => {
        try {
            // 提取前缀字符（空格、中文标点等）
            const prefix = fullMatch.substring(0, fullMatch.indexOf(linkPart));

            // 找到域名后第一个"/"的位置
            const firstSlashIndex = linkPart.indexOf('/');
            if (firstSlashIndex !== -1) {
                // 保留到第一个"/"（包含"/"）
                const processedLink = linkPart.substring(0, firstSlashIndex + 1);
                return prefix + processedLink;
            }

            // 如果没有找到"/"，添加"/"
            return prefix + linkPart + '/';

        } catch (error) {
            // 如果处理失败，返回原始匹配
            return fullMatch;
        }
    });

    return result;
}

/**
 * 处理Excel文件
 * @param {string} inputFilePath - 输入文件路径
 * @param {string} outputFilePath - 输出文件路径
 */
function processExcelFile(inputFilePath, outputFilePath) {
    try {
        console.log(`正在读取文件: ${inputFilePath}`);
        
        // 读取Excel文件
        const workbook = XLSX.readFile(inputFilePath);
        
        // 遍历所有工作表
        workbook.SheetNames.forEach(sheetName => {
            console.log(`正在处理工作表: ${sheetName}`);
            const worksheet = workbook.Sheets[sheetName];
            
            // 遍历工作表中的所有单元格
            Object.keys(worksheet).forEach(cellAddress => {
                if (cellAddress.startsWith('!')) return; // 跳过元数据
                
                const cell = worksheet[cellAddress];
                if (cell && cell.v && typeof cell.v === 'string') {
                    const originalValue = cell.v;
                    const processedValue = processLinkContent(originalValue);
                    
                    if (originalValue !== processedValue) {
                        console.log(`单元格 ${cellAddress} 已处理:`);
                        console.log(`  原始: ${originalValue}`);
                        console.log(`  处理后: ${processedValue}`);
                        cell.v = processedValue;
                        cell.w = processedValue; // 更新显示值
                    }
                }
            });
        });
        
        // 保存处理后的文件
        XLSX.writeFile(workbook, outputFilePath);
        console.log(`文件已保存到: ${outputFilePath}`);
        
    } catch (error) {
        console.error('处理文件时出错:', error.message);
    }
}

/**
 * 主函数
 */
function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('使用方法:');
        console.log('  node index.js <输入文件路径> [输出文件路径]');
        console.log('');
        console.log('示例:');
        console.log('  node index.js input.xlsx');
        console.log('  node index.js input.xlsx output.xlsx');
        console.log('');
        console.log('如果不指定输出文件路径，将在输入文件名后添加"_processed"');
        return;
    }
    
    const inputFile = args[0];
    let outputFile = args[1];
    
    // 检查输入文件是否存在
    if (!fs.existsSync(inputFile)) {
        console.error(`错误: 输入文件不存在: ${inputFile}`);
        return;
    }
    
    // 如果没有指定输出文件，自动生成
    if (!outputFile) {
        const ext = path.extname(inputFile);
        const baseName = path.basename(inputFile, ext);
        const dirName = path.dirname(inputFile);
        outputFile = path.join(dirName, `${baseName}_processed${ext}`);
    }
    
    console.log('Excel链接处理工具');
    console.log('=================');
    console.log(`输入文件: ${inputFile}`);
    console.log(`输出文件: ${outputFile}`);
    console.log('');
    
    processExcelFile(inputFile, outputFile);
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
    main();
}

module.exports = {
    processLinkContent,
    processExcelFile
};
