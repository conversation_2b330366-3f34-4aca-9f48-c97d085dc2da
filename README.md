# Excel链接处理工具

这个Node.js项目用于处理Excel文件中的短信模板内容，自动去除链接中"/"后面的内容，但保留"/"字符。

## 功能特点

- 读取Excel文件（.xlsx格式）
- 自动识别文本中的链接（支持带协议和不带协议）
  - 带协议：`https://example.com/path` 或 `http://example.com/path`
  - 不带协议：`www.example.com/path` 或 `domain.com/path`
- 精确处理各种域名格式（包括子域名如 t.laohu.com）
- 去除域名后第一个"/"之后的所有内容，但保留"/"
- 支持端口号、IP地址、多层子域名等复杂URL格式
- 智能避免误匹配，保持其他文本内容不变
- 支持处理多个工作表
- 生成处理后的新Excel文件

## 安装依赖

```bash
npm install
```

## 使用方法

### 基本用法

```bash
node index.js <输入文件路径> [输出文件路径]
```

### 示例

1. 处理文件并自动生成输出文件名：
```bash
node index.js input.xlsx
```
输出文件将自动命名为 `input_processed.xlsx`

2. 指定输出文件名：
```bash
node index.js input.xlsx output.xlsx
```

### 创建测试文件

运行以下命令创建示例Excel文件进行测试：
```bash
node create-sample.js
```

然后处理示例文件：
```bash
node index.js sample_sms_templates.xlsx
```

## 处理示例

### 带协议链接处理前：
- `https://example.com/details/page1?id=123`
- `https://t.laohu.com/survey/Links`
- `http://mp.3qmp.com/other`
- `https://www.baidu.com/s?wd=test&ie=utf-8`
- `https://localhost:3000/api/users/123`

### 带协议链接处理后：
- `https://example.com/`
- `https://t.laohu.com/`
- `http://mp.3qmp.com/`
- `https://www.baidu.com/`
- `https://localhost:3000/`

### 不带协议链接处理前：
- `www.example.com/path/to/page`
- `t.laohu.com/survey/Links`
- `baidu.com/search?q=test`
- `docs.microsoft.com/zh-cn/windows`
- `stackoverflow.com/questions/12345/how-to-code`

### 不带协议链接处理后：
- `www.example.com/`
- `t.laohu.com/`
- `baidu.com/`
- `docs.microsoft.com/`
- `stackoverflow.com/`

## 文件结构

```
├── package.json          # 项目配置文件
├── index.js             # 主程序文件
├── create-sample.js     # 创建示例文件的脚本
├── README.md           # 说明文档
├── sample_sms_templates.xlsx           # 示例输入文件
└── sample_sms_templates_processed.xlsx # 示例输出文件
```

## 注意事项

1. 支持的文件格式：.xlsx
2. 程序会处理所有工作表中的所有单元格
3. 支持处理带协议（http://、https://）和不带协议的链接
4. 智能识别域名格式，避免误匹配普通文本
5. 保留链接的域名部分和第一个"/"
6. 不会修改原始文件，会生成新的处理后文件

## 技术实现

- 使用 `xlsx` 库读取和写入Excel文件
- 使用正则表达式识别和处理链接
- 支持处理包含多个链接的单元格内容
